<x-public-layout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-gray-900 via-pink-900 to-gray-900 text-white">
        <div class="absolute inset-0 bg-pattern opacity-10"></div>
        <div class="container mx-auto px-4 py-16 md:py-24 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    Find the Perfect <span class="text-pink-300">Escort Agency</span>
                </h1>
                <p class="text-xl text-gray-200 mb-8">
                    Discover top-rated escort agencies offering premium services in Uganda
                </p>

                <!-- Search Form -->
                <div class="bg-white bg-opacity-10 backdrop-blur-sm p-4 rounded-lg shadow-lg mb-10">
                    <form action="{{ route('agencies.search') }}" method="GET" class="flex flex-col md:flex-row gap-3">
                        <div class="flex-grow">
                            <input
                                type="text"
                                name="query"
                                placeholder="Search by name, location or services..."
                                class="w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-pink text-gray-900"
                                value="{{ $query ?? '' }}"
                            >
                        </div>
                        <button type="submit" class="btn btn-primary flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </button>
                    </form>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-2 gap-4 max-w-lg mx-auto">
                    <div class="bg-white bg-opacity-10 rounded-lg p-3 text-center">
                        <div class="text-3xl font-bold">{{ count($agencies) }}</div>
                        <div class="text-sm text-gray-300">Agencies</div>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-3 text-center">
                        <div class="text-3xl font-bold">24/7</div>
                        <div class="text-sm text-gray-300">Service</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            @if(isset($query))
                <div class="mb-10 text-center">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        Search Results for "{{ $query }}"
                    </h2>
                    <p class="text-gray-600">
                        Found {{ $agencies->total() }} {{ Str::plural('agency', $agencies->total()) }}
                    </p>
                </div>
            @else
                <!-- Featured Escorts Section -->
                <div class="mb-16">
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-5 px-4 sm:px-6">
                            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                                <div>
                                    <h2 class="text-2xl font-bold text-white flex items-center">
                                        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        Featured Escorts
                                    </h2>
                                    <p class="text-pink-100 mt-1 max-w-2xl">Our most exclusive and highly-rated companions available for booking</p>
                                </div>
                                <a href="{{ route('escorts.index', ['featured' => 1]) }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-medium px-4 py-2 rounded-md transition-colors duration-300 shadow-sm flex items-center mt-2 sm:mt-0">
                                    View All Featured
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        <div class="p-4 sm:p-6">
                            @if(isset($featuredEscorts) && $featuredEscorts->count() > 0)
                                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
                                    @foreach($featuredEscorts as $escort)
                                        <div class="card group">
                                            <div class="relative pb-[130%]">
                                                @if($escort->primaryImage)
                                                    <img src="{{ asset('storage/' . $escort->primaryImage->image_path) }}" alt="{{ $escort->name }}" class="absolute inset-0 w-full h-full object-cover group-hover:scale-105 transition-transform duration-500">
                                                    <!-- Watermark -->
                                                    <div class="absolute inset-0 flex items-center justify-center">
                                                        <span class="text-white text-opacity-30 font-bold text-2xl transform -rotate-12">GHB</span>
                                                    </div>

                                                    <!-- Media Icons -->
                                                    <div class="absolute top-2 left-2 flex space-x-1">
                                                        @if($escort->images->count() > 1)
                                                            <div class="bg-black bg-opacity-50 rounded-full p-1 text-white">
                                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                                                </svg>
                                                            </div>
                                                        @endif
                                                    </div>

                                                    <!-- Badge - Only show FEATURED badge -->
                                                    <div class="badge badge-featured absolute top-2 right-2 z-10">FEATURED</div>

                                                    <!-- Gradient Overlay with Name and Location -->
                                                    <div class="absolute left-0 right-0 bottom-0 bg-gradient-to-t from-black to-transparent p-4">
                                                        <h3 class="text-xl font-bold text-white">{{ $escort->name }}</h3>
                                                        <div class="text-sm text-gray-300 mt-1">
                                                            <span>{{ $escort->age }} • {{ $escort->locations->count() > 0 ? $escort->locations->first()->name : 'Location N/A' }}</span>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="absolute inset-0 bg-gray-200 flex items-center justify-center">
                                                        <span class="text-gray-400">No image available</span>
                                                    </div>

                                                    <!-- Badge -->
                                                    <div class="badge badge-featured absolute top-2 right-2 z-10">FEATURED</div>
                                                @endif
                                            </div>

                                            <div class="p-4 bg-white">
                                                <div class="flex justify-end items-center mb-2">
                                                    <div class="flex space-x-2">
                                                        @if($escort->incall_available)
                                                            <span class="px-2 py-1 bg-gray-100 text-xs font-medium rounded">Incall</span>
                                                        @endif
                                                        @if($escort->outcall_available)
                                                            <span class="px-2 py-1 bg-gray-100 text-xs font-medium rounded">Outcall</span>
                                                        @endif
                                                    </div>
                                                </div>
                                                <a href="{{ route('escorts.show', $escort->slug) }}" class="block text-center py-2 px-4 border border-pink-500 text-pink-500 rounded hover:bg-pink-500 hover:text-white transition-colors duration-300">
                                                    View Profile
                                                </a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="bg-gray-50 rounded-lg p-8 text-center">
                                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                    <h3 class="text-xl font-medium text-gray-900 mb-2">No Featured Escorts</h3>
                                    <p class="text-gray-500">There are no featured escorts available at the moment.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif


            <!-- All Agencies Section -->
            <div id="all-agencies" class="mb-10">
                @if(!isset($query))
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <span class="inline-block px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full text-xs font-medium">DIRECTORY</span>
                            <h2 class="text-2xl md:text-3xl font-bold text-indigo-600 mt-2 border-b-2 border-indigo-200 pb-2 inline-block">All Agencies</h2>
                        </div>
                        <div class="flex items-center">
                            <span class="text-gray-500 mr-2">Sort by:</span>
                            <select class="bg-white border border-gray-200 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-pink">
                                <option>Newest</option>
                                <option>Most Popular</option>
                                <option>Alphabetical</option>
                            </select>
                        </div>
                    </div>
                @endif

                @if($agencies->count() > 0)
                    <div class="space-y-6">
                        @foreach($agencies as $agency)
                            <div class="agency-card group hover:shadow-xl transform transition-all duration-300 hover:-translate-y-2 border border-gray-200 rounded-xl overflow-hidden bg-white">
                                <a href="{{ route('agencies.show', ['slug' => $agency->slug ?? $agency->id]) }}" class="block">
                                    <div class="flex flex-col md:flex-row">
                                        <!-- Agency Logo -->
                                        <div class="w-full md:w-1/4 bg-gradient-to-r from-blue-50 to-indigo-50 p-6 flex items-center justify-center">
                                            <div class="relative w-full h-40 md:h-full flex items-center justify-center">
                                                @if($agency->logo_path)
                                                    <img
                                                        src="{{ asset('storage/' . $agency->logo_path) }}"
                                                        alt="{{ $agency->name }}"
                                                        class="max-h-full max-w-full object-contain transition-transform duration-500 group-hover:scale-105"
                                                        onerror="this.onerror=null; this.src='{{ asset('images/default-agency-logo.png') }}'"
                                                    >
                                                @else
                                                    <div class="w-full h-full flex items-center justify-center">
                                                        <svg class="w-20 h-20 text-gray-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Card Content -->
                                        <div class="w-full md:w-3/4 p-6">
                                            <div class="flex flex-wrap items-start justify-between mb-2">
                                                <h3 class="text-xl font-bold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">{{ $agency->name }}</h3>

                                                <div class="flex space-x-2 mt-1 md:mt-0">
                                                    <!-- Verified Badge (if applicable) -->
                                                    @if($agency->is_verified)
                                                        <div class="badge badge-verified flex items-center shadow-sm">
                                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                            </svg>
                                                            VERIFIED
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>

                                            @if($agency->address)
                                                <div class="flex items-center text-gray-600 mb-3">
                                                    <svg class="w-4 h-4 mr-2 text-indigo-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    <span>{{ $agency->address }}</span>
                                                </div>
                                            @endif

                                            <p class="text-gray-600 mb-4">
                                                {{ $agency->description ?? 'No description available.' }}
                                            </p>

                                            <div class="flex flex-wrap justify-between items-center pt-3 border-t border-gray-100">
                                                <div class="flex items-center space-x-4 mb-2 md:mb-0">
                                                    <div class="flex items-center text-gray-500 text-sm">
                                                        <svg class="w-4 h-4 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                                        </svg>
                                                        <span>{{ $agency->escorts->count() }} {{ Str::plural('Escort', $agency->escorts->count()) }}</span>
                                                    </div>

                                                    <div class="flex items-center text-gray-500 text-sm">
                                                        <svg class="w-4 h-4 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                        </svg>
                                                        <span>{{ $agency->profile_views ?? 0 }}</span>
                                                    </div>
                                                </div>

                                                <span class="bg-indigo-500 hover:bg-indigo-600 text-white font-medium text-sm flex items-center px-4 py-2 rounded-lg group-hover:translate-x-1 transition-all duration-300">
                                                    View Details
                                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-10 flex justify-center">
                        <div class="bg-white px-4 py-3 rounded-lg shadow">
                            {{ $agencies->links() }}
                        </div>
                    </div>
                @else
                    <div class="bg-white rounded-lg shadow p-8 text-center max-w-2xl mx-auto">
                        <div class="bg-pink bg-opacity-10 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-10 h-10 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3">No agencies found</h3>
                        <p class="text-gray-600 mb-6 text-lg">
                            @if(isset($query))
                                We couldn't find any agencies matching "{{ $query }}".
                            @else
                                There are no agencies available at the moment.
                            @endif
                        </p>
                        <a href="{{ route('agencies.index') }}" class="btn btn-primary shadow">
                            View all agencies
                        </a>
                    </div>
                @endif
            </div>

            <!-- Call to Action Section -->
            @if($agencies->count() > 0)
                <div class="mt-16 bg-gray-900 rounded-lg p-8 md:p-10 shadow-lg relative overflow-hidden">
                    <div class="absolute inset-0 bg-pattern opacity-10"></div>
                    <div class="relative z-10 md:flex items-center justify-between">
                        <div class="md:w-2/3 mb-6 md:mb-0">
                            <h3 class="text-2xl md:text-3xl font-bold text-white mb-3">Are you an agency owner?</h3>
                            <p class="text-gray-300 text-lg">Join our platform and showcase your escorts to thousands of potential clients.</p>
                        </div>
                        <div>
                            <a href="#" class="btn btn-primary font-bold shadow">
                                Register Now
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>
</x-public-layout>
