<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Agency Status Request Details') }}
            </h2>
            <a href="{{ route('admin.agency-status-requests.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {{ __('Back to Requests') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <ul class="list-disc pl-5">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="flex items-center mb-6">
                        <div class="mr-4">
                            @if($statusRequest->status === 'pending')
                                <div class="h-16 w-16 rounded-full bg-yellow-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            @elseif($statusRequest->status === 'approved')
                                <div class="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            @else
                                <div class="h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div>
                            <h3 class="text-xl font-medium text-gray-900">{{ ucfirst($statusRequest->request_type) }} Request</h3>
                            <p class="text-sm text-gray-500">Submitted on {{ $statusRequest->created_at->format('F j, Y') }}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">Agency Details</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Agency Name:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->agency->name }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Email:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->agency->user->email }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Phone:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->agency->phone ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Verified:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->agency->is_verified ? 'Yes' : 'No' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Approved:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->agency->is_approved ? 'Yes' : 'No' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Featured:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->agency->is_featured ? 'Yes' : 'No' }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">Request Details</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Request Type:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ ucfirst($statusRequest->request_type) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Duration:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ ucfirst($statusRequest->duration) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Price:</span>
                                    <span class="text-sm font-medium text-gray-900">UGX {{ number_format($statusRequest->price, 0) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Status:</span>
                                    @if($statusRequest->status === 'pending')
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    @elseif($statusRequest->status === 'approved')
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Approved
                                        </span>
                                    @else
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            Rejected
                                        </span>
                                    @endif
                                </div>
                                @if($statusRequest->approved_at)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Approved On:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->approved_at->format('F j, Y') }}</span>
                                </div>
                                @endif
                                @if($statusRequest->expires_at)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Expires On:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $statusRequest->expires_at->format('F j, Y') }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    @if($statusRequest->status === 'pending')
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="text-lg font-medium text-gray-900 mb-2">Approve Request</h4>
                                <form action="{{ route('admin.agency-status-requests.approve', $statusRequest->id) }}" method="POST">
                                    @csrf
                                    <div class="mb-4">
                                        <label for="admin_message" class="block text-sm font-medium text-gray-700 mb-1">Message (Optional)</label>
                                        <textarea id="admin_message" name="admin_message" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"></textarea>
                                    </div>
                                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        Approve Request
                                    </button>
                                </form>
                            </div>

                            <div class="bg-red-50 p-4 rounded-lg">
                                <h4 class="text-lg font-medium text-gray-900 mb-2">Reject Request</h4>
                                <form action="{{ route('admin.agency-status-requests.reject', $statusRequest->id) }}" method="POST">
                                    @csrf
                                    <div class="mb-4">
                                        <label for="admin_message" class="block text-sm font-medium text-gray-700 mb-1">Reason for Rejection</label>
                                        <textarea id="admin_message" name="admin_message" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" required></textarea>
                                    </div>
                                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        Reject Request
                                    </button>
                                </form>
                            </div>
                        </div>
                    @else
                        <div class="bg-gray-50 p-4 rounded-lg mb-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">Admin Response</h4>
                            @if($statusRequest->admin_message)
                                <p class="text-sm text-gray-700">{{ $statusRequest->admin_message }}</p>
                            @else
                                <p class="text-sm text-gray-500">No message provided.</p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
