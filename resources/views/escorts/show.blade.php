<x-public-layout>
    <style>
        /* Watermark */
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-30deg);
            font-size: 36px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.3);
            pointer-events: none;
            z-index: 5;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            white-space: nowrap;
        }

        /* Tab styling */
        .tab-button {
            position: relative;
            transition: all 0.3s;
        }

        .tab-button.active {
            color: #ec4899;
            border-bottom: 2px solid #ec4899;
        }

        .tab-button:not(.active) {
            color: #6b7280;
        }

        .tab-button:hover:not(.active) {
            color: #374151;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Thumbnail styling */
        .thumbnail {
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .thumbnail:hover {
            border-color: #ec4899;
        }

        .thumbnail.active {
            border-color: #ec4899;
        }
    </style>

    <!-- Breadcrumb Navigation -->
    <div class="bg-gray-50 py-3 border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex text-sm">
                <a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700">Home</a>
                <span class="mx-2 text-gray-400">/</span>
                <a href="{{ route('escorts.index') }}" class="text-gray-500 hover:text-gray-700">Escorts</a>
                <span class="mx-2 text-gray-400">/</span>
                <span class="text-gray-900 font-medium">{{ $escort->name }}</span>
            </nav>
        </div>
    </div>

    <div class="py-8 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- E-commerce Style Product Detail Layout -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Product Header Section -->
                <div class="border-b border-gray-200 px-6 py-4">
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">{{ $escort->name }}</h1>
                            <div class="flex items-center mt-1">
                                <div class="flex items-center">
                                    @if($escort->locations->count() > 0)
                                        <span class="text-gray-600 text-sm flex items-center mr-4">
                                            <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            {{ $escort->locations->first()->name }}
                                        </span>
                                    @endif
                                    <span class="text-gray-600 text-sm flex items-center mr-4">
                                        <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        {{ $escort->age }} years
                                    </span>
                                    <span class="text-gray-600 text-sm flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        {{ $escort->profile_views }} views
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 mt-2 md:mt-0">
                            @if($escort->is_verified)
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Verified
                                </span>
                            @endif
                            @if($escort->is_featured)
                                <span class="bg-pink-100 text-pink-800 text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                    Featured
                                </span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="flex flex-col lg:flex-row">
                    <!-- Left Column: Media Gallery -->
                    <div class="lg:w-3/5 p-6">
                        @if($escort->images->count() > 0)
                            @php
                                $mainImage = $escort->images->where('is_primary', true)->first()
                                    ?? $escort->images->where('is_main', true)->first()
                                    ?? $escort->images->first();
                                $imagePath = $mainImage->image_path ?? $mainImage->path;
                            @endphp

                            <!-- Main Image Display -->
                            <div class="relative rounded-lg overflow-hidden mb-4 border border-gray-200">
                                <img
                                    src="{{ asset('storage/' . $imagePath) }}"
                                    alt="{{ $escort->name }}"
                                    class="w-full object-cover h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px]"
                                    id="main-image"
                                >
                                <!-- Watermark -->
                                <div class="watermark">GHB</div>

                                <!-- Full Screen Button -->
                                <button id="fullscreen-btn" class="absolute top-3 right-3 bg-white bg-opacity-80 hover:bg-opacity-100 text-gray-800 p-2 rounded-full transition-all duration-200 transform hover:scale-110 shadow-md">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                                    </svg>
                                </button>

                                <!-- Video Play Button (shown only for video content) -->
                                @if($escort->id % 3 == 0 && $mainImage->id % 3 == 0)
                                    <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                                        <button class="bg-white bg-opacity-75 hover:bg-opacity-100 rounded-full p-4 transition-all duration-300 transform hover:scale-110">
                                            <svg class="w-10 h-10 text-pink" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                @endif

                                <!-- Fullscreen Modal -->
                                <div id="fullscreen-modal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden">
                                    <div class="relative w-full h-full flex items-center justify-center p-4">
                                        <!-- Navigation Arrows -->
                                        <div class="absolute inset-y-0 left-0 flex items-center">
                                            <button id="prev-image" class="ml-4 md:ml-8 z-10 bg-white hover:bg-gray-100 text-gray-800 p-3 md:p-4 rounded-full transition-all duration-200 transform hover:scale-110 shadow-lg">
                                                <svg class="w-6 h-6 md:w-8 md:h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                                </svg>
                                            </button>
                                        </div>

                                        <!-- Main Image -->
                                        <img id="fullscreen-image" src="{{ asset('storage/' . $imagePath) }}" alt="{{ $escort->name }}" class="max-w-full max-h-full object-contain shadow-2xl">

                                        <!-- Right Arrow -->
                                        <div class="absolute inset-y-0 right-0 flex items-center">
                                            <button id="next-image" class="mr-4 md:mr-8 z-10 bg-white hover:bg-gray-100 text-gray-800 p-3 md:p-4 rounded-full transition-all duration-200 transform hover:scale-110 shadow-lg">
                                                <svg class="w-6 h-6 md:w-8 md:h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </button>
                                        </div>

                                        <!-- Close Button - Large and Prominent -->
                                        <button id="close-fullscreen" class="fixed top-6 right-6 z-50 bg-pink hover:bg-pink-600 text-white px-6 py-3 rounded-lg transition-all duration-200 shadow-lg flex items-center text-lg font-medium">
                                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Close
                                        </button>

                                        <!-- Image Counter -->
                                        <div id="fullscreen-counter" class="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-center bg-white text-gray-800 text-sm font-medium py-2 px-6 rounded-full shadow-lg">
                                            <span id="current-index">1</span> <span class="text-gray-500 mx-1">/</span> <span id="total-images">{{ $escort->images->count() }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Thumbnail Selector -->
                            <div class="flex overflow-x-auto space-x-2 py-2 pb-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                @foreach($escort->images->take(8) as $index => $image)
                                    @php
                                        $thumbPath = $image->image_path ?? $image->path;
                                    @endphp
                                    <div
                                        class="thumbnail flex-shrink-0 rounded-md overflow-hidden cursor-pointer border-2 {{ $mainImage && $mainImage->id == $image->id ? 'border-pink' : 'border-transparent' }}"
                                        data-image-path="{{ asset('storage/' . $thumbPath) }}"
                                        data-image-id="{{ $image->id }}"
                                    >
                                        <div class="w-14 h-14 sm:w-16 sm:h-16 bg-gray-100 relative">
                                            <img
                                                src="{{ asset('storage/' . $thumbPath) }}"
                                                alt="{{ $escort->name }} - Image {{ $index + 1 }}"
                                                class="object-cover w-full h-full"
                                            >

                                            <!-- Video Indicator -->
                                            @if($escort->id % 3 == 0 && $image->id % 3 == 0)
                                                <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Media Count Indicators -->
                            <div class="flex mt-4 space-x-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $escort->images->count() }} Photos
                                </div>
                                @if($escort->id % 3 == 0)
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                                        </svg>
                                        2 Videos
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="w-full h-96 bg-gray-200 flex items-center justify-center rounded-lg">
                                <span class="text-gray-400">No images available</span>
                            </div>
                        @endif
                    </div>

                    <!-- Right Column: Escort Details -->
                    <div class="lg:w-2/5 p-6 lg:border-l border-gray-200">
                        <!-- Quick Stats -->
                        <div class="grid grid-cols-2 sm:grid-cols-2 gap-2 sm:gap-4 mb-6">
                            <div class="bg-gray-50 p-2 sm:p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Height</p>
                                <p class="text-gray-800 text-sm sm:text-base font-medium">{{ $escort->height_cm }} cm</p>
                            </div>
                            <div class="bg-gray-50 p-2 sm:p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Build</p>
                                <p class="text-gray-800 text-sm sm:text-base font-medium">{{ $escort->build }}</p>
                            </div>
                            <div class="bg-gray-50 p-2 sm:p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Hair</p>
                                <p class="text-gray-800 text-sm sm:text-base font-medium">{{ $escort->hair_color }}</p>
                            </div>
                            <div class="bg-gray-50 p-2 sm:p-3 rounded-md">
                                <p class="text-gray-500 text-xs mb-1">Ethnicity</p>
                                <p class="text-gray-800 text-sm sm:text-base font-medium">{{ $escort->ethnicity }}</p>
                            </div>
                        </div>

                        <!-- Availability -->
                        <div class="mb-6">
                            <h2 class="text-lg font-semibold text-gray-800 mb-3">Availability</h2>
                            <div class="flex space-x-4">
                                <div class="flex-1 bg-gray-50 p-3 rounded-md text-center {{ $escort->incall_available ? 'border-l-4 border-green-500' : 'border-l-4 border-gray-300' }}">
                                    <p class="text-gray-500 text-xs mb-1">Incall</p>
                                    <p class="text-gray-800 font-medium">{{ $escort->incall_available ? 'Available' : 'Not Available' }}</p>
                                </div>
                                <div class="flex-1 bg-gray-50 p-3 rounded-md text-center {{ $escort->outcall_available ? 'border-l-4 border-green-500' : 'border-l-4 border-gray-300' }}">
                                    <p class="text-gray-500 text-xs mb-1">Outcall</p>
                                    <p class="text-gray-800 font-medium">{{ $escort->outcall_available ? 'Available' : 'Not Available' }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Rates Preview -->
                        @if($escort->rates->count() > 0)
                            <div class="mb-6">
                                <h2 class="text-lg font-semibold text-gray-800 mb-3">Rates</h2>
                                <div class="bg-gray-50 rounded-md p-3">
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-gray-600">{{ $escort->rates->first()->duration }}</span>
                                        <span class="font-semibold text-gray-900">
                                            @if($escort->incall_available && $escort->rates->first()->min_incall_price)
                                                UGX {{ number_format($escort->rates->first()->min_incall_price) }} - {{ number_format($escort->rates->first()->max_incall_price) }}
                                            @elseif($escort->outcall_available && $escort->rates->first()->min_outcall_price)
                                                UGX {{ number_format($escort->rates->first()->min_outcall_price) }} - {{ number_format($escort->rates->first()->max_outcall_price) }}
                                            @else
                                                Contact for pricing
                                            @endif
                                        </span>
                                    </div>
                                    <a href="#rates" class="text-pink hover:text-pink-700 text-sm font-medium">View all rates</a>
                                </div>
                            </div>
                        @endif

                        <!-- Contact Information -->
                        <div class="mt-6 space-y-3">
                            @if($escort->show_phone_number && $escort->phone_number)
                                <div class="flex items-center justify-between bg-gray-50 p-4 rounded-md">
                                    <div class="flex items-center">
                                        <a href="tel:{{ $escort->phone_number }}" class="flex items-center text-gray-800 hover:text-pink-600 transition-colors duration-300">
                                            <svg class="w-5 h-5 text-pink-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            <span class="font-medium">{{ $escort->phone_number }}</span>
                                        </a>
                                    </div>
                                    @if($escort->show_whatsapp && $escort->whatsapp_number)
                                        <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $escort->whatsapp_number) }}" target="_blank" class="bg-green-500 hover:bg-green-600 text-white p-3 rounded-md transition-colors duration-300 flex items-center">
                                            <svg class="w-5 h-5 mr-2" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                                <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                                            </svg>
                                            <span class="font-medium">WhatsApp</span>
                                        </a>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Tabs Section -->
                <div class="mt-8 border-t border-gray-200">
                    <!-- Tabs Navigation -->
                    <div class="border-b border-gray-200">
                        <div class="flex -mb-px">
                            <button id="tab-about" class="tab-button px-6 py-3 font-medium active" onclick="switchTab('about')">About</button>
                            <button id="tab-services" class="tab-button px-6 py-3 font-medium" onclick="switchTab('services')">Services</button>
                            <button id="tab-rates" class="tab-button px-6 py-3 font-medium" onclick="switchTab('rates')">Rates</button>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- About Content -->
                        <div id="content-about" class="tab-content active">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">About {{ $escort->name }}</h2>

                            @if($escort->about)
                                <div class="prose max-w-none mb-6">
                                    <p class="text-gray-700">{{ $escort->about }}</p>
                                </div>
                            @else
                                <p class="text-gray-500 mb-6">No description provided.</p>
                            @endif

                            <!-- Languages -->
                            @if($escort->languages->count() > 0)
                                <h3 class="text-lg font-semibold text-gray-800 mb-3">Languages</h3>
                                <div class="flex flex-wrap gap-2 mb-6">
                                    @foreach($escort->languages as $language)
                                        <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                                            {{ $language->name }} ({{ ucfirst($language->pivot->proficiency) }})
                                        </span>
                                    @endforeach
                                </div>
                            @endif
                        </div>

                        <!-- Services Content -->
                        <div id="content-services" class="tab-content">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">Services Offered</h2>

                            @if($escort->services->count() > 0)
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                                    @foreach($escort->services as $service)
                                        <div class="flex items-center p-3 bg-gray-50 rounded-md">
                                            <svg class="w-5 h-5 text-pink mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span class="text-gray-800">{{ $service->name }}</span>
                                        </div>
                                    @endforeach
                                </div>
                                <p class="text-sm text-gray-500 italic">Please contact for detailed information about specific services and rates.</p>
                            @else
                                <p class="text-gray-500 mb-6">No services listed. Please contact for more information.</p>
                            @endif
                        </div>

                        <!-- Rates Content -->
                        <div id="content-rates" class="tab-content">
                            <h2 class="text-xl font-bold text-gray-800 mb-4">Rates</h2>

                            @if($escort->rates->count() > 0)
                                <!-- Mobile Rates (Cards) -->
                                <div class="block md:hidden space-y-3 mb-6">
                                    @foreach($escort->rates as $rate)
                                        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                            <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                                                <h3 class="font-medium text-gray-900">{{ $rate->duration }}</h3>
                                            </div>
                                            <div class="p-4 space-y-3">
                                                @if($escort->incall_available)
                                                    <div>
                                                        <p class="text-xs text-gray-500 mb-1">Incall</p>
                                                        <p class="text-sm font-medium text-gray-900">
                                                            @if($rate->min_incall_price && $rate->max_incall_price)
                                                                UGX {{ number_format($rate->min_incall_price) }} - {{ number_format($rate->max_incall_price) }}
                                                            @else
                                                                N/A
                                                            @endif
                                                        </p>
                                                    </div>
                                                @endif
                                                @if($escort->outcall_available)
                                                    <div>
                                                        <p class="text-xs text-gray-500 mb-1">Outcall</p>
                                                        <p class="text-sm font-medium text-gray-900">
                                                            @if($rate->min_outcall_price && $rate->max_outcall_price)
                                                                UGX {{ number_format($rate->min_outcall_price) }} - {{ number_format($rate->max_outcall_price) }}
                                                            @else
                                                                N/A
                                                            @endif
                                                        </p>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <!-- Desktop Rates (Table) -->
                                <div class="hidden md:block overflow-hidden rounded-lg border border-gray-200 mb-6">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                                @if($escort->incall_available)
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Incall</th>
                                                @endif
                                                @if($escort->outcall_available)
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outcall</th>
                                                @endif
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            @foreach($escort->rates as $rate)
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $rate->duration }}</td>
                                                    @if($escort->incall_available)
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                            @if($rate->min_incall_price && $rate->max_incall_price)
                                                                UGX {{ number_format($rate->min_incall_price) }} - {{ number_format($rate->max_incall_price) }}
                                                            @else
                                                                N/A
                                                            @endif
                                                        </td>
                                                    @endif
                                                    @if($escort->outcall_available)
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                            @if($rate->min_outcall_price && $rate->max_outcall_price)
                                                                UGX {{ number_format($rate->min_outcall_price) }} - {{ number_format($rate->max_outcall_price) }}
                                                            @else
                                                                N/A
                                                            @endif
                                                        </td>
                                                    @endif
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                @if($escort->rates->first()->description)
                                    <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                        <h3 class="text-sm font-semibold text-gray-700 mb-2">Additional Rate Information:</h3>
                                        <p class="text-sm text-gray-600">{{ $escort->rates->first()->description }}</p>
                                    </div>
                                @endif
                                <p class="text-sm text-gray-500 italic mt-4">Rates are subject to change. Please confirm when booking.</p>
                            @else
                                <p class="text-gray-500 mb-6">No rates listed. Please contact for pricing information.</p>
                            @endif
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Image Gallery and Fullscreen -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Image Gallery Variables
            const mainImage = document.getElementById('main-image');
            const thumbnails = document.querySelectorAll('.thumbnail');
            const fullscreenBtn = document.getElementById('fullscreen-btn');
            const fullscreenModal = document.getElementById('fullscreen-modal');
            const fullscreenImage = document.getElementById('fullscreen-image');
            const closeFullscreenBtn = document.getElementById('close-fullscreen');
            const prevImageBtn = document.getElementById('prev-image');
            const nextImageBtn = document.getElementById('next-image');
            const currentIndexEl = document.getElementById('current-index');
            const totalImagesEl = document.getElementById('total-images');

            let currentImageIndex = 0;
            const imagePaths = [];

            // Collect all image paths and their indices
            thumbnails.forEach((thumbnail, index) => {
                imagePaths.push({
                    path: thumbnail.getAttribute('data-image-path'),
                    id: thumbnail.getAttribute('data-image-id')
                });

                // Set initial index for the currently displayed image
                if (thumbnail.classList.contains('active')) {
                    currentImageIndex = index;
                }
            });

            // Update fullscreen image and counter
            function updateFullscreenImage() {
                fullscreenImage.src = imagePaths[currentImageIndex].path;
                currentIndexEl.textContent = currentImageIndex + 1;

                // Update main image and thumbnail selection too
                mainImage.src = imagePaths[currentImageIndex].path;

                // Update active thumbnail
                thumbnails.forEach((thumbnail, index) => {
                    if (index === currentImageIndex) {
                        thumbnail.classList.add('active');
                    } else {
                        thumbnail.classList.remove('active');
                    }
                });
            }

            // Thumbnail click functionality
            thumbnails.forEach((thumbnail, index) => {
                thumbnail.addEventListener('click', function() {
                    // Remove active class from all thumbnails
                    thumbnails.forEach(t => t.classList.remove('active'));

                    // Add active class to clicked thumbnail
                    this.classList.add('active');

                    // Update current index
                    currentImageIndex = index;

                    // Update main image
                    const imagePath = this.getAttribute('data-image-path');
                    mainImage.src = imagePath;
                    fullscreenImage.src = imagePath;
                });
            });

            // Previous image button
            prevImageBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                currentImageIndex = (currentImageIndex - 1 + imagePaths.length) % imagePaths.length;
                updateFullscreenImage();
            });

            // Next image button
            nextImageBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                currentImageIndex = (currentImageIndex + 1) % imagePaths.length;
                updateFullscreenImage();
            });

            // Fullscreen functionality
            fullscreenBtn.addEventListener('click', function() {
                fullscreenModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
                updateFullscreenImage(); // Ensure counter and image are in sync
            });

            closeFullscreenBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                fullscreenModal.classList.add('hidden');
                document.body.style.overflow = ''; // Restore scrolling
            });

            // Close fullscreen when clicking outside the image
            fullscreenModal.addEventListener('click', function(e) {
                if (e.target === fullscreenModal) {
                    fullscreenModal.classList.add('hidden');
                    document.body.style.overflow = '';
                }
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (!fullscreenModal.classList.contains('hidden')) {
                    // Close with Escape key
                    if (e.key === 'Escape') {
                        fullscreenModal.classList.add('hidden');
                        document.body.style.overflow = '';
                    }
                    // Previous image with left arrow
                    else if (e.key === 'ArrowLeft') {
                        currentImageIndex = (currentImageIndex - 1 + imagePaths.length) % imagePaths.length;
                        updateFullscreenImage();
                    }
                    // Next image with right arrow
                    else if (e.key === 'ArrowRight') {
                        currentImageIndex = (currentImageIndex + 1) % imagePaths.length;
                        updateFullscreenImage();
                    }
                }
            });
        });

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Deactivate all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Activate selected tab and content
            document.getElementById('content-' + tabName).classList.add('active');
            document.getElementById('tab-' + tabName).classList.add('active');
        }
    </script>
</x-public-layout>