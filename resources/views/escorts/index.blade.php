<x-public-layout>
    <style data-version="1.3">
        /* Watermark - keeping this as custom CSS since it's specialized */
        .watermark {
            @apply absolute top-1/2 left-1/2 text-4xl font-bold text-white text-opacity-30 pointer-events-none z-10 whitespace-nowrap;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            transform: translate(-50%, -50%) rotate(-30deg);
        }

        /* Media indicators - keeping as custom CSS for specialized styling */
        .media-indicators {
            @apply absolute top-2.5 left-2.5 flex gap-1.5 z-10;
        }

        .media-indicator {
            @apply flex items-center justify-center w-7 h-7 rounded-full bg-black bg-opacity-60 text-white text-sm;
        }

        /* Custom transform for card hover that's not in Tailwind by default */
        .hover-translate-y-sm:hover {
            @apply -translate-y-0.5;
        }
    </style>

    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-gray-900 to-pink-900 text-white py-16 sm:py-20 md:py-24 overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-60"></div>
        <div class="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00eiIvPjwvZz48L2c+PC9zdmc+')]"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-4 sm:mb-6 text-white drop-shadow-md">Find Your Perfect Escort</h1>
            <p class="text-lg sm:text-xl md:text-2xl text-white text-opacity-90 max-w-3xl mx-auto mb-8 sm:mb-10 drop-shadow-sm">Browse our selection of verified escorts available for booking</p>
        </div>
    </div>

    <div class="py-12 sm:py-16 md:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Filters and Search -->
            <section id="filters" class="mb-16">
                <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-5 px-4 sm:px-6">
                        <div>
                            <h2 class="text-2xl font-bold text-white flex items-center">
                                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Find Your Perfect Match
                            </h2>
                            <p class="text-pink-100 mt-1 max-w-2xl">Use our advanced filters to find exactly what you're looking for</p>
                        </div>
                    </div>
                    <form action="{{ route('escorts.search') }}" method="GET" class="p-4 sm:p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                            <!-- Gender Filter -->
                            <div>
                                <label for="gender" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    I'm looking for
                                </label>
                                <select id="gender" name="gender" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink shadow-sm">
                                    <option value="">All Genders</option>
                                    <option value="female" {{ request('gender') == 'female' ? 'selected' : '' }}>Female Escorts</option>
                                    <option value="male" {{ request('gender') == 'male' ? 'selected' : '' }}>Male Escorts</option>
                                    <option value="couple" {{ request('gender') == 'couple' ? 'selected' : '' }}>Couples</option>
                                    <option value="transsexual" {{ request('gender') == 'transsexual' ? 'selected' : '' }}>Trans Escorts</option>
                                </select>
                            </div>

                            <!-- Location Filter -->
                            <div>
                                <label for="location_id" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Location
                                </label>
                                <select id="location_id" name="location_id" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink shadow-sm">
                                    <option value="">All Locations</option>
                                    @foreach($locations as $location)
                                        <option value="{{ $location->id }}" {{ request('location_id') == $location->id ? 'selected' : '' }}>
                                            {{ $location->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Service Filter -->
                            <div>
                                <label for="service_id" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                    </svg>
                                    Service
                                </label>
                                <select id="service_id" name="service_id" class="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink focus:border-pink shadow-sm">
                                    <option value="">All Services</option>
                                    @foreach($services as $service)
                                        <option value="{{ $service->id }}" {{ request('service_id') == $service->id ? 'selected' : '' }}>
                                            {{ $service->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Availability Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Availability
                                </label>
                                <div class="grid grid-cols-2 gap-2 sm:gap-3">
                                    <label class="inline-flex items-center bg-gray-100 rounded-md px-2 sm:px-3 py-2 cursor-pointer hover:bg-gray-200 transition-colors duration-200">
                                        <input type="checkbox" name="incall" value="1" {{ request('incall') ? 'checked' : '' }} class="rounded border-gray-300 text-pink focus:ring-pink mr-1 sm:mr-2">
                                        <span class="text-xs sm:text-sm text-gray-700">Incall</span>
                                    </label>
                                    <label class="inline-flex items-center bg-gray-100 rounded-md px-2 sm:px-3 py-2 cursor-pointer hover:bg-gray-200 transition-colors duration-200">
                                        <input type="checkbox" name="outcall" value="1" {{ request('outcall') ? 'checked' : '' }} class="rounded border-gray-300 text-pink focus:ring-pink mr-1 sm:mr-2">
                                        <span class="text-xs sm:text-sm text-gray-700">Outcall</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="mt-4 sm:mt-6">
                            <button type="submit" class="w-full bg-pink hover:bg-pink-600 text-white font-semibold py-2 sm:py-3 px-4 rounded-md transition-colors duration-300 shadow-md flex items-center justify-center">
                                <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <span class="text-sm sm:text-base">Search Escorts</span>
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Featured Escorts Section -->
            <section id="featured" class="mb-16">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-5 px-4 sm:px-6">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                            <div>
                                <h2 class="text-2xl font-bold text-white flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    Featured Escorts
                                    </h2>
                                    <p class="text-pink-100 mt-1 max-w-2xl">Our most exclusive and highly-rated companions available for booking</p>
                                </div>
                                <a href="{{ route('escorts.index', ['featured' => 1]) }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-medium px-4 py-2 rounded-md transition-colors duration-300 shadow-sm flex items-center mt-2 sm:mt-0">
                                    View All Featured
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        <div class="p-4 sm:p-6">
                            @if($featuredEscorts->count() > 0)
                                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-5 lg:gap-6">
                                    @foreach($featuredEscorts as $escort)
                                        <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover-translate-y-sm flex flex-col h-full group">
                                            <div class="relative pb-[130%] overflow-hidden">
                                                @if($escort->primaryImage)
                                                    <img src="{{ asset('storage/' . $escort->primaryImage->image_path) }}" alt="{{ $escort->name }}" class="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-105">
                                                    <!-- Watermark -->
                                                    <div class="watermark">GHB</div>

                                                    <!-- Media indicators -->
                                                    <div class="media-indicators">
                                                        @if($escort->images->count() > 1)
                                                            <div class="media-indicator" title="{{ $escort->images->count() }} Photos">
                                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                                                </svg>
                                                            </div>
                                                        @endif
                                                        <!-- For demonstration purposes, show video icon for escorts with ID divisible by 3 -->
                                                        @if($escort->id % 3 == 0)
                                                            <div class="media-indicator" title="Has Videos">
                                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                                                                </svg>
                                                            </div>
                                                        @endif
                                                    </div>
                                                @else
                                                    <div class="absolute inset-0 bg-gray-200 flex items-center justify-center">
                                                        <span class="text-gray-400">No image available</span>
                                                    </div>
                                                @endif

                                                <!-- Badge - Only show FEATURED badge -->
                                                <div class="absolute top-3 right-3 bg-pink-500 text-white text-xs font-bold py-1 px-3 rounded shadow-md z-10">FEATURED</div>

                                                <!-- Gradient Overlay with Name and Location -->
                                                <div class="absolute left-0 right-0 bottom-0 w-full bg-gradient-to-t from-black via-black/60 to-transparent pt-16 pb-3 sm:pb-5 px-3 sm:px-4">
                                                    <h3 class="text-lg sm:text-xl font-bold text-white truncate">{{ $escort->name }}</h3>
                                                    <div class="text-xs sm:text-sm text-gray-300 mt-1 truncate">
                                                        <span>{{ $escort->age }} • {{ $escort->locations->count() > 0 ? $escort->locations->first()->name : 'Location N/A' }}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="p-3 sm:p-4 flex-grow">
                                                <div class="flex justify-end items-center mb-2">
                                                    <div class="flex space-x-1 sm:space-x-2">
                                                        @if($escort->services->where('name', 'Incall')->count() > 0)
                                                            <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-xs font-medium text-gray-700 rounded">Incall</span>
                                                        @endif
                                                        @if($escort->services->where('name', 'Outcall')->count() > 0)
                                                            <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-xs font-medium text-gray-700 rounded">Outcall</span>
                                                        @endif
                                                    </div>
                                                </div>
                                                <a href="{{ route('escorts.show', $escort->slug) }}" class="block w-full text-center py-1.5 sm:py-2 border border-pink-500 text-pink-500 hover:bg-pink-500 hover:text-white text-sm sm:text-base font-medium rounded transition-colors duration-300 mt-2">View Profile</a>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="bg-gray-50 rounded-lg p-8 text-center">
                                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    <p class="text-gray-700 text-lg mb-4">No featured escorts available at the moment.</p>
                                    <p class="text-gray-600">Check back soon for our featured selections!</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </section>



            <!-- Main Escorts List -->
            <section id="all-escorts" class="mb-16">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-pink-600 to-pink-800 py-5 px-4 sm:px-6">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                            <div>
                                <h2 class="text-2xl font-bold text-white flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    All Escorts
                                </h2>
                                <p class="text-pink-100 mt-1 max-w-2xl">Browse our complete selection of available escorts in your area</p>
                            </div>
                            <div class="bg-white bg-opacity-20 px-4 py-2 rounded-md shadow-sm mt-2 sm:mt-0">
                                <span class="text-white font-medium">{{ $escorts->total() }}</span>
                                <span class="text-pink-100"> escorts found</span>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 sm:p-6">
                        @if($escorts->count() > 0)
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-5 lg:gap-6">
                                @foreach($escorts as $escort)
                                    <div class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover-translate-y-sm flex flex-col h-full group">
                                        <div class="relative pb-[130%] overflow-hidden">
                                            @if($escort->primaryImage)
                                                <img src="{{ asset('storage/' . $escort->primaryImage->image_path) }}" alt="{{ $escort->name }}" class="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-105">
                                                <!-- Watermark -->
                                                <div class="watermark">GHB</div>

                                                <!-- Media indicators -->
                                                <div class="media-indicators">
                                                    @if($escort->images->count() > 1)
                                                        <div class="media-indicator" title="{{ $escort->images->count() }} Photos">
                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </div>
                                                    @endif
                                                    <!-- For demonstration purposes, show video icon for escorts with ID divisible by 3 -->
                                                    @if($escort->id % 3 == 0)
                                                        <div class="media-indicator" title="Has Videos">
                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                                                            </svg>
                                                        </div>
                                                    @endif
                                                </div>
                                            @else
                                                <div class="absolute inset-0 bg-gray-200 flex items-center justify-center">
                                                    <span class="text-gray-400">No image available</span>
                                                </div>
                                            @endif

                                            <!-- Badge -->
                                            @if($escort->is_verified)
                                                <div class="absolute top-3 right-3 bg-green-500 text-white text-xs font-bold py-1 px-3 rounded shadow-md z-10">VERIFIED</div>
                                            @elseif($escort->is_new)
                                                <div class="absolute top-3 right-3 bg-orange-500 text-white text-xs font-bold py-1 px-3 rounded shadow-md z-10">NEW</div>
                                            @elseif($escort->is_featured)
                                                <div class="absolute top-3 right-3 bg-pink-500 text-white text-xs font-bold py-1 px-3 rounded shadow-md z-10">FEATURED</div>
                                            @endif

                                            <!-- Gradient Overlay with Name and Location -->
                                            <div class="absolute left-0 right-0 bottom-0 w-full bg-gradient-to-t from-black via-black/60 to-transparent pt-16 pb-3 sm:pb-5 px-3 sm:px-4">
                                                <h3 class="text-lg sm:text-xl font-bold text-white truncate">{{ $escort->name }}</h3>
                                                <div class="text-xs sm:text-sm text-gray-300 mt-1 truncate">
                                                    <span>{{ $escort->age }} • {{ $escort->locations->count() > 0 ? $escort->locations->first()->name : 'Location N/A' }}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="p-3 sm:p-4 flex-grow">
                                            <div class="flex justify-end items-center mb-2">
                                                <div class="flex space-x-1 sm:space-x-2">
                                                    @if($escort->services->where('name', 'Incall')->count() > 0)
                                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-xs font-medium text-gray-700 rounded">Incall</span>
                                                    @endif
                                                    @if($escort->services->where('name', 'Outcall')->count() > 0)
                                                        <span class="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-xs font-medium text-gray-700 rounded">Outcall</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <a href="{{ route('escorts.show', $escort->slug) }}" class="block w-full text-center py-1.5 sm:py-2 border border-pink-500 text-pink-500 hover:bg-pink-500 hover:text-white text-sm sm:text-base font-medium rounded transition-colors duration-300 mt-2">View Profile</a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Pagination -->
                            <div class="mt-8">
                                {{ $escorts->withQueryString()->links() }}
                            </div>
                        @else
                            <div class="bg-gray-50 rounded-lg p-8 text-center">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <p class="text-gray-700 text-lg mb-4">No escorts found matching your criteria.</p>
                                <a href="{{ route('escorts.index') }}" class="inline-block bg-pink hover:bg-pink-600 text-white font-medium px-4 py-2 rounded-md transition-colors duration-300 shadow-md">
                                    Clear Filters
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </section>
        </div>
    </div>
</x-public-layout>
