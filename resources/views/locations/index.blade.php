<x-home-layout>
    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-indigo-800 via-purple-700 to-pink-700 text-white py-16 sm:py-20 md:py-24 overflow-hidden">
        <!-- Pattern overlay -->
        <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>

        <!-- Dot Pattern Overlay -->
        <div class="absolute inset-0 bg-pattern opacity-40" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC42Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00eiIvPjwvZz48L2c+PC9zdmc+');"></div>

        <!-- Dark overlay gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-black/20"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 sm:mb-8 text-white drop-shadow-lg tracking-tight">Browse by Location</h1>
            <p class="text-lg sm:text-xl md:text-2xl text-white max-w-3xl mx-auto mb-8 sm:mb-10 drop-shadow-md leading-relaxed">Find escorts and agencies in your area or anywhere you plan to visit</p>

            <!-- Search Icon -->
            <div class="inline-flex items-center justify-center p-4 bg-white/15 backdrop-blur-sm rounded-full mt-4 animate-pulse shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="py-12 relative">
        <!-- Subtle pattern background -->
        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%239C92AC" fill-opacity="0.4" fill-rule="evenodd"%3E%3Ccircle cx="3" cy="3" r="1"%2F%3E%3Ccircle cx="13" cy="13" r="1"%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E');"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($countriesWithCities as $countryData)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 border border-gray-200 transform hover:-translate-y-1">
                        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 py-5 px-6">
                            <h2 class="text-2xl font-bold text-white drop-shadow-sm">{{ $countryData['country']->name }}</h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-5">
                                @foreach($countryData['cities'] as $cityData)
                                    <div class="border-b border-gray-200 pb-5 last:border-b-0 last:pb-0">
                                        <h3 class="text-lg font-semibold mb-3 flex items-center">
                                            <svg class="w-5 h-5 text-indigo-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            <a href="{{ route('locations.show', $cityData['city']->slug) }}" class="text-indigo-600 hover:text-pink-600 transition-colors duration-300 font-medium">
                                                {{ $cityData['city']->name }}
                                            </a>
                                        </h3>

                                        <div class="grid grid-cols-2 gap-3 pl-7">
                                            @foreach($cityData['areas'] as $area)
                                                <a href="{{ route('locations.show', $area->slug) }}" class="text-gray-800 hover:text-pink-600 text-sm flex items-center transition-colors duration-300 py-1">
                                                    <span class="w-1.5 h-1.5 bg-indigo-500 rounded-full mr-2 flex-shrink-0"></span>
                                                    {{ $area->name }}
                                                </a>
                                            @endforeach

                                            @if($cityData['area_count'] > 6)
                                                <a href="{{ route('locations.show', $cityData['city']->slug) }}" class="text-pink-600 hover:text-pink-700 text-sm font-medium transition-colors duration-300 flex items-center py-1">
                                                    <svg class="w-3.5 h-3.5 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ $cityData['area_count'] - 6 }} more areas
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Call to Action Section -->
            <div class="mt-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg shadow-xl overflow-hidden border-2 border-indigo-700">
                <div class="px-6 py-12 md:py-16 md:px-12 text-center sm:text-left sm:flex sm:items-center sm:justify-between">
                    <div>
                        <h3 class="text-2xl sm:text-3xl font-bold text-white drop-shadow-md">Can't find your location?</h3>
                        <p class="mt-3 text-white text-sm sm:text-base max-w-md leading-relaxed">Contact us to request adding a new location or browse our full selection of escorts.</p>
                    </div>
                    <div class="mt-8 sm:mt-0">
                        <a href="{{ route('escorts.index') }}" class="inline-flex items-center justify-center px-6 py-3 bg-white text-indigo-600 font-semibold rounded-lg shadow-md hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-base">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Browse All Escorts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-home-layout>