<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Get Hot Babes') }}</title>

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
        <link rel="icon" href="{{ asset('favicon.ico') }}" sizes="any">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body>
        <div class="min-h-screen flex flex-col bg-gray-900">
            <!-- Header -->
            <header class="bg-gray-900 border-b border-gray-800 py-4">
                <div class="container mx-auto px-4 flex justify-center">
                    <a href="/" class="text-2xl font-bold text-white">
                        <span class="text-pink-500">Get Hot</span> Babes
                    </a>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
                <div class="w-full max-w-md">
                    <div class="bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden">
                        @if(isset($title))
                            <div class="bg-gray-900 border-b border-gray-700 py-4 px-6">
                                <h2 class="text-xl font-bold text-center text-white">{{ $title }}</h2>
                            </div>
                        @endif

                        <div class="p-6">
                            {{ $slot }}
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="mt-6 text-center text-gray-400 text-sm">
                        <p>&copy; {{ date('Y') }} {{ $settings['site_name'] ?? 'Get Hot Babes' }}. All rights reserved.</p>
                        <p class="mt-1 text-pink-500">{{ $settings['footer_text'] ?? 'Must be 18+ to use this service.' }}</p>
                    </div>
                </div>
            </main>

            <!-- Global Confirmation Modal -->
            <x-confirmation-modal />
        </div>
    </body>
</html>
