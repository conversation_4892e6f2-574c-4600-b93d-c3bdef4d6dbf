@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Components */
@layer components {
    /* Container */
    .container {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    /* Background Pattern */
    .bg-pattern {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v6h6v-6h-6zm6 6v6h-6v-6h6zm-6-12v6h6v-6h-6zm12 6v6h6v-6h-6zm-6 12v6h6v-6h-6zm12 0v6h6v-6h-6zm-12-24v6h6v-6h-6zm12 0v6h6v-6h-6zm-6 12v6h6v-6h-6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    /* Agency Cards */
    .agency-card {
        @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 border border-gray-100 h-full flex flex-col;
    }

    .agency-card:hover {
        @apply shadow-xl border-pink-500 border-opacity-20;
    }

    .agency-card:hover img {
        @apply scale-105;
    }

    .agency-card:hover h3 {
        @apply text-pink-500;
    }

    .agency-card img {
        @apply transition-transform duration-500 max-h-full max-w-full object-contain;
    }

    /* Badges */
    .badge {
        @apply inline-block px-3 py-1 rounded-full text-xs font-medium shadow-sm;
    }



    .badge-verified {
        @apply bg-gradient-to-r from-green-500 to-green-600 text-white;
    }

    .badge-featured {
        @apply bg-gradient-to-r from-pink-500 to-pink-600 text-white;
    }

    /* Buttons */
    .btn {
        @apply inline-block rounded-lg font-medium transition-colors duration-300;
    }

    .btn-primary {
        @apply bg-pink-500 hover:bg-pink-600 text-white py-3 px-6;
    }

    .btn-secondary {
        @apply bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4;
    }

    /* Line clamp for descriptions */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

/* Custom Styles */
@layer base {
    body {
        @apply font-sans text-gray-800 antialiased;
    }
    h1, h2, h3, h4, h5, h6 {
        @apply font-serif font-bold;
    }
}

@layer components {
    .btn {
        @apply inline-flex items-center justify-center px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium text-sm sm:text-base transition-colors duration-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
    }
    .btn-primary {
        @apply btn bg-pink-500 text-white hover:bg-pink-600 focus:ring-pink-500 border border-transparent;
    }
    .btn-secondary {
        @apply btn border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 hover:text-pink-500 focus:ring-pink-500;
    }
    .btn-dark {
        @apply btn border border-gray-700 text-white hover:bg-gray-800 focus:ring-gray-700;
    }
    .btn-sm {
        @apply px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm;
    }
    .btn-lg {
        @apply px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg;
    }

    .card {
        @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl border border-gray-100;
    }

    .form-input {
        @apply w-full border border-gray-300 rounded-md py-2 px-3 text-sm sm:text-base text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500;
    }

    .form-input-dark {
        @apply w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-sm sm:text-base text-gray-300 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500;
    }

    .section-title {
        @apply text-3xl md:text-4xl font-bold text-gray-900 mb-4;
    }

    .section-description {
        @apply max-w-2xl mx-auto text-lg text-gray-600;
    }
}

/* Line clamp utilities */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Notification badge */
.notification-badge {
    position: relative;
    top: -8px;
    right: -2px;
    background-color: theme('colors.pink.500');
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

/* Mobile Responsiveness Improvements */
.watermark {
    @apply absolute inset-0 flex items-center justify-center pointer-events-none;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.3);
    font-weight: bold;
    transform: rotate(-30deg);
}

@media (min-width: 640px) {
    .watermark {
        font-size: 2.5rem;
    }
}

@media (min-width: 768px) {
    .watermark {
        font-size: 3rem;
    }
}

/* Media indicators */
.media-indicators {
    @apply absolute bottom-3 left-3 flex space-x-1.5;
}

.media-indicator {
    @apply bg-black bg-opacity-60 text-white p-1 rounded-full;
}

/* Thumbnail styling */
.thumbnail {
    @apply cursor-pointer transition-all duration-200;
}

.thumbnail.active {
    @apply ring-2 ring-pink-500;
}

/* Responsive tables */
.responsive-table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
}

.responsive-table table {
    width: 100%;
    min-width: 640px;
}

@media (max-width: 640px) {
    .responsive-table th,
    .responsive-table td {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    .responsive-table th {
        white-space: nowrap;
    }
}

/* Improved scrollbars for webkit browsers */
.scrollbar-thin::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Tab styling */
.tab-button {
    @apply text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
}

.tab-button.active {
    @apply text-pink-600 border-pink-500;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}
