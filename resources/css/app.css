@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Components */
@layer components {
    /* Container */
    .container {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    /* Background Pattern */
    .bg-pattern {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v6h6v-6h-6zm6 6v6h-6v-6h6zm-6-12v6h6v-6h-6zm12 6v6h6v-6h-6zm-6 12v6h6v-6h-6zm12 0v6h6v-6h-6zm-12-24v6h6v-6h-6zm12 0v6h6v-6h-6zm-6 12v6h6v-6h-6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    /* Agency Cards */
    .agency-card {
        @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 border border-gray-100 h-full flex flex-col;
    }

    .agency-card:hover {
        @apply shadow-xl border-pink-500 border-opacity-20;
    }

    .agency-card:hover img {
        @apply scale-105;
    }

    .agency-card:hover h3 {
        @apply text-pink-500;
    }

    .agency-card img {
        @apply transition-transform duration-500 max-h-full max-w-full object-contain;
    }

    /* Badges */
    .badge {
        @apply inline-block px-3 py-1 rounded-full text-xs font-medium shadow-sm;
    }



    .badge-verified {
        @apply bg-gradient-to-r from-green-500 to-green-600 text-white;
    }

    .badge-featured {
        @apply bg-gradient-to-r from-pink-500 to-pink-600 text-white;
    }

}

/* Custom Styles */
@layer base {
    body {
        @apply font-sans text-gray-800 antialiased;
    }
    h1, h2, h3, h4, h5, h6 {
        @apply font-serif font-bold;
    }
}

@layer components {
    /* Buttons - Unified button system */
    .btn {
        @apply inline-flex items-center justify-center px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-medium text-sm sm:text-base transition-all duration-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
    }
    .btn-primary {
        @apply btn bg-pink-500 text-white hover:bg-pink-600 focus:ring-pink-500 border border-transparent;
    }
    .btn-secondary {
        @apply btn border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 hover:text-pink-500 focus:ring-pink-500;
    }
    .btn-dark {
        @apply btn bg-gray-800 border border-gray-700 text-white hover:bg-gray-900 focus:ring-gray-700;
    }
    .btn-sm {
        @apply px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm;
    }
    .btn-lg {
        @apply px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg;
    }

    /* Cards */
    .card {
        @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl border border-gray-100;
    }

    /* Form inputs */
    .form-input {
        @apply w-full border border-gray-300 rounded-lg py-2.5 px-4 text-sm sm:text-base text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-colors duration-200;
    }

    .form-input-dark {
        @apply w-full bg-gray-700 border border-gray-600 rounded-lg py-2.5 px-4 text-sm sm:text-base text-gray-300 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-colors duration-200;
    }

    /* Typography */
    .section-title {
        @apply text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4;
    }

    .section-description {
        @apply max-w-2xl mx-auto text-base sm:text-lg text-gray-600;
    }
}

/* Line clamp utilities */
@layer utilities {
    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

/* Notification badge */
@layer components {
    .notification-badge {
        @apply relative -top-2 -right-0.5 bg-pink-500 text-white rounded-full w-4.5 h-4.5 inline-flex items-center justify-center text-xs font-bold;
    }
}

/* Mobile Responsiveness Improvements */
@layer components {
    .watermark {
        @apply absolute inset-0 flex items-center justify-center pointer-events-none text-2xl sm:text-3xl md:text-4xl text-white text-opacity-30 font-bold transform -rotate-30;
    }

    /* Media indicators */
    .media-indicators {
        @apply absolute bottom-3 left-3 flex space-x-1.5;
    }

    .media-indicator {
        @apply bg-black bg-opacity-60 text-white p-1 rounded-full;
    }

    /* Thumbnail styling */
    .thumbnail {
        @apply cursor-pointer transition-all duration-200 hover:opacity-80;
    }

    .thumbnail.active {
        @apply ring-2 ring-pink-500;
    }
}

/* Responsive tables */
@layer components {
    .responsive-table {
        @apply block w-full overflow-x-auto mb-4 rounded-lg;
        -webkit-overflow-scrolling: touch;
    }

    .responsive-table table {
        @apply w-full min-w-full sm:min-w-0;
        min-width: 640px;
    }

    /* Tab styling */
    .tab-button {
        @apply text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 transition-colors duration-200 px-4 py-2 font-medium;
    }

    .tab-button.active {
        @apply text-pink-600 border-pink-500;
    }

    .tab-content {
        @apply hidden;
    }

    .tab-content.active {
        @apply block;
    }
}

/* Improved scrollbars for webkit browsers */
@layer utilities {
    .scrollbar-thin::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
        background: theme('colors.gray.100');
        border-radius: 10px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
        background: theme('colors.gray.400');
        border-radius: 10px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
        background: theme('colors.gray.600');
    }
}

/* Responsive table mobile adjustments */
@media (max-width: 640px) {
    .responsive-table th,
    .responsive-table td {
        @apply px-3;
    }

    .responsive-table th {
        @apply whitespace-nowrap;
    }
}
