<?php

namespace App\Http\Controllers\Escort;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\BlacklistedContact;

class BlacklistController extends Controller
{
    /**
     * Display a listing of the escort's blacklisted contacts.
     */
    public function index()
    {
        $escort = Auth::user()->escort;
        $blacklistedContacts = $escort->blacklistedContacts()->latest()->paginate(10);

        return view('escort.blacklist.index', compact('escort', 'blacklistedContacts'));
    }

    /**
     * Store a newly created blacklisted contact in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'reason' => 'required|string|max:1000',
        ]);

        $escort = Auth::user()->escort;

        $blacklistedContact = new BlacklistedContact();
        $blacklistedContact->escort_id = $escort->id;
        $blacklistedContact->name = $request->name;
        $blacklistedContact->phone = $request->phone;
        $blacklistedContact->email = $request->email;
        $blacklistedContact->description = $request->reason;
        $blacklistedContact->save();

        return redirect()->route('escort.blacklist')->with('success', 'Contact added to blacklist.');
    }

    /**
     * Remove the specified blacklisted contact from storage.
     */
    public function destroy($id)
    {
        $escort = Auth::user()->escort;
        $blacklistedContact = BlacklistedContact::where('id', $id)
            ->where('escort_id', $escort->id)
            ->firstOrFail();

        $blacklistedContact->delete();

        return redirect()->route('escort.blacklist')->with('success', 'Contact removed from blacklist.');
    }
}
