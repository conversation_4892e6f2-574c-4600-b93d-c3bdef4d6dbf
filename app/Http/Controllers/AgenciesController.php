<?php

namespace App\Http\Controllers;

use App\Models\Agency;
use App\Models\Escort;
use Illuminate\Http\Request;

class AgenciesController extends Controller
{
    /**
     * Display a listing of agencies.
     */
    public function index()
    {
        // Get featured escorts for the top section
        $featuredEscorts = Escort::with(['primaryImage', 'rates', 'locations'])
            ->where('is_featured', true)
            ->verified()
            ->latest()
            ->take(4)
            ->get();

        // Get verified and approved agencies with pagination
        $agencies = Agency::verified()
            ->approved()
            ->with('user')
            ->latest()
            ->paginate(12);

        return view('agencies.index', compact('agencies', 'featuredEscorts'));
    }

    /**
     * Display the specified agency.
     */
    public function show($id)
    {
        $agency = Agency::verified()
            ->approved()
            ->with(['escorts' => function($query) {
                $query->verified()->orderBy('is_verified', 'desc')->latest();
            }])
            ->findOrFail($id);

        return view('agencies.show', compact('agency'));
    }
}