<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the appropriate dashboard based on user type.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();

        if ($user->isAdmin()) {
            return view('dashboard.admin');
        } elseif ($user->isEscort()) {
            // Load escort with relationships needed for the dashboard
            $user->load(['escort.images']);
            return view('dashboard.escort');
        } elseif ($user->isAgency()) {
            return view('dashboard.agency');
        } else {
            // Redirect to home if user type is not recognized
            return redirect()->route('home');
        }
    }
}
