<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\BlacklistedContact;

class BlacklistController extends Controller
{
    /**
     * Display a listing of the agency's blacklisted contacts.
     */
    public function index()
    {
        $agency = Auth::user()->agency;
        $blacklistedContacts = BlacklistedContact::whereHas('escort', function($query) use ($agency) {
            $query->where('agency_id', $agency->id);
        })->with('escort')->latest()->paginate(10);

        return view('agency.blacklist.index', compact('agency', 'blacklistedContacts'));
    }

    /**
     * Store a newly created blacklisted contact in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'reason' => 'required|string|max:1000',
            'escort_id' => 'required|exists:escorts,id',
        ]);

        $agency = Auth::user()->agency;

        // Verify escort belongs to this agency
        $escortBelongsToAgency = $agency->escorts()->where('id', $request->escort_id)->exists();

        if (!$escortBelongsToAgency) {
            return redirect()->back()->with('error', 'Invalid escort selected.');
        }

        $blacklistedContact = new BlacklistedContact();
        $blacklistedContact->escort_id = $request->escort_id;
        $blacklistedContact->name = $request->name;
        $blacklistedContact->phone = $request->phone;
        $blacklistedContact->email = $request->email;
        $blacklistedContact->description = $request->reason;
        $blacklistedContact->save();

        return redirect()->route('agency.blacklist')->with('success', 'Contact added to blacklist.');
    }

    /**
     * Remove the specified blacklisted contact from storage.
     */
    public function destroy($id)
    {
        $agency = Auth::user()->agency;

        $blacklistedContact = BlacklistedContact::whereHas('escort', function($query) use ($agency) {
            $query->where('agency_id', $agency->id);
        })->where('id', $id)->firstOrFail();

        $blacklistedContact->delete();

        return redirect()->route('agency.blacklist')->with('success', 'Contact removed from blacklist.');
    }
}
