<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AgencyStatusRequest;
use App\Models\Agency;
use App\Services\NotificationService;
use Carbon\Carbon;

class AgencyStatusRequestController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of all agency status requests.
     */
    public function index(Request $request)
    {
        $query = AgencyStatusRequest::with('agency');

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('request_type') && $request->request_type) {
            $query->where('request_type', $request->request_type);
        }

        $statusRequests = $query->latest()->paginate(15);

        return view('admin.agency-status-requests.index', compact('statusRequests'));
    }

    /**
     * Display the specified status request.
     */
    public function show($id)
    {
        $statusRequest = AgencyStatusRequest::with('agency')->findOrFail($id);

        return view('admin.agency-status-requests.show', compact('statusRequest'));
    }

    /**
     * Approve the specified status request.
     */
    public function approve(Request $request, $id)
    {
        $statusRequest = AgencyStatusRequest::with('agency')->findOrFail($id);

        // Only approve pending requests
        if ($statusRequest->status !== 'pending') {
            return redirect()->back()->withErrors([
                'status' => 'This request has already been ' . $statusRequest->status . '.'
            ]);
        }

        $request->validate([
            'admin_message' => 'nullable|string',
        ]);

        // Update the status request
        $statusRequest->status = 'approved';
        $statusRequest->admin_message = $request->admin_message;
        $statusRequest->approved_at = now();

        // Set expiration date based on duration
        if ($statusRequest->duration) {
            $expiresAt = match ($statusRequest->duration) {
                'day' => now()->addDay(),
                'week' => now()->addWeek(),
                'month' => now()->addMonth(),
                'annual' => now()->addYear(),
                default => null,
            };
            $statusRequest->expires_at = $expiresAt;
        }

        $statusRequest->save();

        // Update the agency's status
        $agency = $statusRequest->agency;

        if ($statusRequest->request_type === 'approval') {
            $agency->is_approved = true;
        } elseif ($statusRequest->request_type === 'featured') {
            $agency->is_featured = true;
        }

        $agency->save();

        // Notify the agency
        $this->notificationService->create(
            $agency->user,
            'status_request_approved',
            ucfirst($statusRequest->request_type) . ' Request Approved',
            'Your request to be ' . $statusRequest->request_type . ' has been approved.',
            [
                'request_id' => $statusRequest->id,
                'request_type' => $statusRequest->request_type,
                'admin_message' => $statusRequest->admin_message,
                'expires_at' => $statusRequest->expires_at ? $statusRequest->expires_at->format('Y-m-d H:i:s') : null
            ]
        );

        return redirect()->route('admin.agency-status-requests.index')->with('success', 'Request approved successfully.');
    }

    /**
     * Reject the specified status request.
     */
    public function reject(Request $request, $id)
    {
        $statusRequest = AgencyStatusRequest::with('agency')->findOrFail($id);

        // Only reject pending requests
        if ($statusRequest->status !== 'pending') {
            return redirect()->back()->withErrors([
                'status' => 'This request has already been ' . $statusRequest->status . '.'
            ]);
        }

        $request->validate([
            'admin_message' => 'required|string',
        ]);

        // Update the status request
        $statusRequest->status = 'rejected';
        $statusRequest->admin_message = $request->admin_message;
        $statusRequest->save();

        // Notify the agency
        $this->notificationService->create(
            $statusRequest->agency->user,
            'status_request_rejected',
            ucfirst($statusRequest->request_type) . ' Request Rejected',
            'Your request to be ' . $statusRequest->request_type . ' has been rejected.',
            [
                'request_id' => $statusRequest->id,
                'request_type' => $statusRequest->request_type,
                'admin_message' => $statusRequest->admin_message
            ]
        );

        return redirect()->route('admin.agency-status-requests.index')->with('success', 'Request rejected successfully.');
    }
}
