<?php

namespace App\Http\Controllers;

use App\Models\Escort;
use App\Models\Location;
use App\Models\Service;
use Illuminate\Http\Request;

class EscortsController extends Controller
{
    /**
     * Display a listing of escorts.
     */
    public function index(Request $request)
    {
        $query = Escort::with(['primaryImage', 'rates', 'locations']);

        // Apply filters if provided
        if ($request->has('gender')) {
            $query->where('gender', $request->gender);
        }

        if ($request->has('location_id')) {
            $query->whereHas('locations', function ($q) use ($request) {
                $q->where('location_id', $request->location_id);
            });
        }

        if ($request->has('service_id')) {
            $query->whereHas('services', function ($q) use ($request) {
                $q->where('service_id', $request->service_id);
            });
        }

        if ($request->has('incall') && $request->incall) {
            $query->where('incall_available', true);
        }

        if ($request->has('outcall') && $request->outcall) {
            $query->where('outcall_available', true);
        }

        // Get featured escorts for the top section - only show escorts marked as featured
        $featuredEscorts = Escort::with(['primaryImage', 'rates', 'locations'])
            ->where('is_featured', true)
            ->latest()
            ->take(4)
            ->get();

        // Get the main list of escorts with pagination, prioritizing verified escorts
        $escorts = $query->orderBy('is_verified', 'desc')->latest()->paginate(12);

        // Get locations and services for filters
        $locations = Location::where('type', 'city')->orderBy('name')->get();
        $services = Service::orderBy('name')->get();

        return view('escorts.index', compact('escorts', 'featuredEscorts', 'locations', 'services'));
    }

    /**
     * Display the specified escort.
     */
    public function show($slug)
    {
        $escort = Escort::with([
            'images',
            'services',
            'rates',
            'locations',
            'languages',
            'user.profile'
        ])->where('slug', $slug)->firstOrFail();

        // Increment view count
        $escort->incrementViews();

        // Get similar escorts (same gender, location)
        $similarEscorts = Escort::with(['primaryImage'])
            ->where('id', '!=', $escort->id)
            ->where('gender', $escort->gender);

        // Only filter by location if the escort has locations
        if ($escort->locations->count() > 0) {
            $similarEscorts->whereHas('locations', function ($query) use ($escort) {
                $query->whereIn('location_id', $escort->locations->pluck('id'));
            });
        }

        $similarEscorts = $similarEscorts->orderBy('is_verified', 'desc')->take(4)->get();

        return view('escorts.show', compact('escort', 'similarEscorts'));
    }

    /**
     * Search for escorts based on criteria.
     */
    public function search(Request $request)
    {
        return $this->index($request);
    }
}
