<?php

namespace App\Http\Controllers;

use App\Models\Escort;
use App\Models\Location;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        // Get featured escorts for the home page
        $featuredEscorts = Escort::with(['primaryImage', 'rates', 'locations', 'services'])
            ->where('is_featured', true)
            ->verified()
            ->latest()
            ->take(4)
            ->get();

        // Get popular locations
        $popularLocations = Location::active()
            ->ofType('city')
            ->withCount('escorts')
            ->orderBy('escorts_count', 'desc')
            ->take(4)
            ->get();

        return view('home', compact('featuredEscorts', 'popularLocations'));
    }
}
