<?php

namespace App\Services;

use App\Models\User;
use App\Models\Notification;

class NotificationService
{
    /**
     * Create a new notification for a user.
     *
     * @param User $user
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return Notification
     */
    public function create(User $user, string $type, string $title, string $message, ?array $data = null): Notification
    {
        return Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'is_read' => false,
        ]);
    }

    /**
     * Create a new notification for multiple users.
     *
     * @param array $userIds
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return void
     */
    public function createForMultipleUsers(array $userIds, string $type, string $title, string $message, ?array $data = null): void
    {
        $notifications = [];
        
        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'is_read' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        
        if (!empty($notifications)) {
            Notification::insert($notifications);
        }
    }

    /**
     * Create a new notification for all users of a specific type.
     *
     * @param string $userType
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return void
     */
    public function createForUserType(string $userType, string $type, string $title, string $message, ?array $data = null): void
    {
        $userIds = User::where('user_type', $userType)->pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, $type, $title, $message, $data);
    }

    /**
     * Create a new notification for all users.
     *
     * @param string $type
     * @param string $title
     * @param string $message
     * @param array|null $data
     * @return void
     */
    public function createForAllUsers(string $type, string $title, string $message, ?array $data = null): void
    {
        $userIds = User::pluck('id')->toArray();
        $this->createForMultipleUsers($userIds, $type, $title, $message, $data);
    }
}
