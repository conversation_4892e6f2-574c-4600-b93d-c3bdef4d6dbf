<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlacklistedContact extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'blacklisted_contacts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'escort_id',
        'agency_id',
        'phone',
        'email',
        'description',
    ];

    /**
     * Get the escort that blacklisted the contact.
     */
    public function escort()
    {
        return $this->belongsTo(Escort::class);
    }

    /**
     * Get the agency that blacklisted the contact.
     */
    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }
}
