<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Agency extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'slug',
        'description',
        'logo_path',
        'website',
        'phone',
        'email',
        'address',
        'is_verified',
        'is_premium',
        'is_featured',
        'is_approved',
        'profile_views',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_verified' => 'boolean',
        'is_premium' => 'boolean',
        'is_featured' => 'boolean',
        'is_approved' => 'boolean',
        'profile_views' => 'integer',
    ];

    /**
     * Get the user that owns the agency.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the escorts for the agency.
     */
    public function escorts()
    {
        return $this->hasMany(Escort::class);
    }



    /**
     * Get the blacklisted contacts for the agency.
     */
    public function blacklistedContacts()
    {
        return $this->hasMany(BlacklistedContact::class);
    }

    /**
     * Scope a query to only include verified agencies.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope a query to only include premium agencies.
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * Scope a query to only include featured agencies.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include approved agencies.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Get the status requests for the agency.
     */
    public function statusRequests()
    {
        return $this->hasMany(AgencyStatusRequest::class);
    }
}
