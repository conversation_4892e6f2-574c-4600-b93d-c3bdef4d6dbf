<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlacklistedClient extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'escort_id',
        'agency_id',
        'phone',
        'email',
        'description',
    ];

    /**
     * Get the escort that blacklisted the client.
     */
    public function escort()
    {
        return $this->belongsTo(Escort::class);
    }

    /**
     * Get the agency that blacklisted the client.
     */
    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }
}
