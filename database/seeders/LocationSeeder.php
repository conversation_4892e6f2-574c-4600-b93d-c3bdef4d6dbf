<?php

namespace Database\Seeders;

use App\Models\Location;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First create countries
        $countries = [
            'United States',
            'United Kingdom',
            'France',
            'Germany',
            'Japan',
            'Australia',
        ];

        $countryIds = [];
        foreach ($countries as $country) {
            $location = Location::create([
                'name' => $country,
                'type' => 'country',
                'parent_id' => null,
                'slug' => Str::slug($country),
                'is_active' => true,
            ]);
            $countryIds[$country] = $location->id;
        }

        // Then create cities
        $cities = [
            ['name' => 'New York', 'country' => 'United States'],
            ['name' => 'Los Angeles', 'country' => 'United States'],
            ['name' => 'Chicago', 'country' => 'United States'],
            ['name' => 'Miami', 'country' => 'United States'],
            ['name' => 'Las Vegas', 'country' => 'United States'],
            ['name' => 'London', 'country' => 'United Kingdom'],
            ['name' => 'Paris', 'country' => 'France'],
            ['name' => 'Berlin', 'country' => 'Germany'],
            ['name' => 'Tokyo', 'country' => 'Japan'],
            ['name' => 'Sydney', 'country' => 'Australia'],
        ];

        foreach ($cities as $city) {
            Location::create([
                'name' => $city['name'],
                'type' => 'city',
                'parent_id' => $countryIds[$city['country']],
                'slug' => Str::slug($city['name']),
                'is_active' => true,
            ]);
        }
    }
}
