<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EscortLocationsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all escort IDs
        $escortIds = DB::table('escorts')->pluck('id')->toArray();

        // Get city location IDs (excluding countries)
        $cityLocations = DB::table('locations')->where('type', 'city')->pluck('id')->toArray();

        // Define primary locations for each escort
        $primaryLocations = [
            1 => 5, // Sophia in Kampala
            2 => 11, // Emma in Nairobi
            3 => 6, // Olivia in Entebbe
            4 => 13, // Ava in Dar es Salaam
            5 => 14, // Isabella in Kigali
        ];

        // Assign locations to escorts
        foreach ($escortIds as $escortId) {
            // Assign primary location
            $primaryLocationId = $primaryLocations[$escortId] ?? $cityLocations[array_rand($cityLocations)];

            DB::table('escort_locations')->insert([
                'escort_id' => $escortId,
                'location_id' => $primaryLocationId,
                'is_primary' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Assign 1-3 additional random locations
            $additionalLocationsCount = rand(1, 3);
            $availableLocations = array_diff($cityLocations, [$primaryLocationId]);
            $selectedLocations = array_rand(array_flip($availableLocations), min($additionalLocationsCount, count($availableLocations)));

            if (!is_array($selectedLocations)) {
                $selectedLocations = [$selectedLocations];
            }

            foreach ($selectedLocations as $locationId) {
                DB::table('escort_locations')->insert([
                    'escort_id' => $escortId,
                    'location_id' => $locationId,
                    'is_primary' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
