<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General settings
            [
                'key' => 'site_name',
                'value' => 'Get Hot Babes',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The name of the website',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'The premier platform for high-class escorts and elite companions. We connect discerning clients with sophisticated escorts for unforgettable experiences.',
                'group' => 'general',
                'type' => 'textarea',
                'description' => 'A short description of the website',
                'is_public' => true,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Find Your Perfect Companion',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The tagline of the website',
                'is_public' => true,
            ],
            [
                'key' => 'currency',
                'value' => 'UGX',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The currency used on the website',
                'is_public' => true,
            ],
            [
                'key' => 'currency_symbol',
                'value' => 'UGX',
                'group' => 'general',
                'type' => 'text',
                'description' => 'The currency symbol used on the website',
                'is_public' => true,
            ],
            
            // Contact information
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'group' => 'contact',
                'type' => 'email',
                'description' => 'The contact email address',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+256 700 000000',
                'group' => 'contact',
                'type' => 'text',
                'description' => 'The contact phone number',
                'is_public' => true,
            ],
            
            // Footer settings
            [
                'key' => 'footer_text',
                'value' => 'Must be 18+ to use this service.',
                'group' => 'footer',
                'type' => 'text',
                'description' => 'Text displayed in the footer',
                'is_public' => true,
            ],
            [
                'key' => 'footer_about',
                'value' => 'The premier platform for high-class escorts and elite companions. We connect discerning clients with sophisticated escorts for unforgettable experiences.',
                'group' => 'footer',
                'type' => 'textarea',
                'description' => 'About text displayed in the footer',
                'is_public' => true,
            ],
            
            // Social media links
            [
                'key' => 'social_facebook',
                'value' => 'https://facebook.com/gethotbabes',
                'group' => 'social',
                'type' => 'url',
                'description' => 'Facebook page URL',
                'is_public' => true,
            ],
            [
                'key' => 'social_twitter',
                'value' => 'https://twitter.com/gethotbabes',
                'group' => 'social',
                'type' => 'url',
                'description' => 'Twitter profile URL',
                'is_public' => true,
            ],
            [
                'key' => 'social_instagram',
                'value' => 'https://instagram.com/gethotbabes',
                'group' => 'social',
                'type' => 'url',
                'description' => 'Instagram profile URL',
                'is_public' => true,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::firstOrCreate(['key' => $setting['key']], $setting);
        }
    }
}
